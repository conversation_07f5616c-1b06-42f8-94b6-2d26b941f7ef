import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:gsheets/gsheets.dart';
import 'package:retry/retry.dart';
import 'package:songanh/config/gsheet.dart';
import 'package:songanh/page/history_page.dart';
import 'package:songanh/page/home_page.dart';
import 'package:songanh/routes.dart';
import 'package:songanh/theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  final remoteConfig = FirebaseRemoteConfig.instance;
  await remoteConfig.setConfigSettings(RemoteConfigSettings(
    fetchTimeout: const Duration(minutes: 1),
    minimumFetchInterval: const Duration(hours: 1),
  ));
// await remoteConfig.setDefaults(const {
//     "value": '123',
//     "value2": '3.14159',
//     "value3": 'true',
//     "value4": "Hello, world!",
// });
  await retry<bool>(
    // Make a GET request
    () => FirebaseRemoteConfig.instance
        .fetchAndActivate()
        .timeout(const Duration(seconds: 60)),
    // Retry on FirebaseException or TimeoutException
    retryIf: (e) => e is FirebaseException || e is TimeoutException,
  );
  await FirebaseRemoteConfig.instance.setDefaults(const {
    "example_param_1": 42,
    "example_param_2": 3.14159,
    "example_param_3": true,
    "testapp": "Hello, world!",
  });

  final spreadsheetId = remoteConfig.getString('spreadsheetId');
  final credentialsJson = remoteConfig.getValue('keygsheet');
  GsheetConfig.gsheets = GSheets(credentialsJson.asString());
  GsheetConfig.spreadsheet =
      await GsheetConfig.gsheets.spreadsheet(spreadsheetId);

  runApp(MyApp(
    spreadsheetId: spreadsheetId,
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key, required this.spreadsheetId});
  static bool firstTimeOneTab = false;
  static bool firstTimeSecondTab = false;
  final String spreadsheetId;
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      locale: const Locale('vi', 'VN'),
      supportedLocales: const [
        Locale('vi', 'VN'),
        Locale('en', 'US'), 
      ],
      localizationsDelegates: const [
        // Các delegate mặc định của Flutter
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      title: 'Flutter Demo',
      theme: AppTheme.theme,
      home: FutureBuilder<List<Worksheet>>(
          future: GsheetConfig.initListWork(spreadsheetId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                child: CircularProgressIndicator(),
              );
            }
            if (snapshot.connectionState == ConnectionState.done) {
              return MyHomePage(
                works: snapshot.data ?? [],
              );
            }
            return SizedBox();
          }),
    );
  }
}

class MyHomePage extends StatelessWidget {
  const MyHomePage({super.key, required this.works});
  final List<Worksheet> works;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton.extended(
        label: Text('Tổng quan sơ đồ'),
        onPressed: () {
        AppNavigator.pushAllRoom(context, works.first);
      }),
      body: DefaultTabController(
        initialIndex: 0,
        length: works.length,
        child: Scaffold(
          appBar: AppBar(
            bottom: TabBar(
              indicatorSize:TabBarIndicatorSize.tab ,
          //    indicatorColor: Colors.amber,
          //    dividerHeight: 0,
               
          //     indicator: BoxDecoration(
          //   color: Colors.blue.withValues(alpha: .1), 
           
          //   borderRadius: BorderRadius.only(topLeft: Radius.circular(8),
          //   topRight: Radius.circular(8)
          //   ),
          // ),
           
              tabs: [
                ...List.generate(works.length, (i) {
                  final tab = works[i].title;
                  return Tab(
                    text: tab,
                  );
                })
              ],
            ),
          ),
          body: TabBarView(children: [
            HomePage(
              wSheet: works.first,
            ),
            HistoryPage(
              wSheet: works.last,
            )
          ]),
        ),
      ),
    );
  }
}
