import 'package:flutter/material.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/widgets/consumer_card.dart';

class ExpandedAnimatedCard extends StatelessWidget {
  const ExpandedAnimatedCard({super.key, required this.controller, required this.nRoom});
  final AnimationController controller;
  final ValueNotifier<Room?> nRoom;
  @override
  Widget build(BuildContext context) {
    return SizeTransition(
      sizeFactor: controller,
      child: ValueListenableBuilder(
        valueListenable: nRoom,
        builder: (context,vRoom, child) {
          return vRoom != null? Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                  ColoredBox(
                    color: Colors.grey.withValues(alpha: .2),
                    child: SizedBox(
                      width: double.infinity,
                    
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          spacing: 6,
                          children: [
                            Text(vRoom.roomId??'', style: Theme.of(context).textTheme.titleLarge,),
                           if (vRoom.type == 'VIP')
                            DecoratedBox(
                              decoration: BoxDecoration(
                                  color: Colors.yellow,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                child: Text('VIP',style: Theme.of(context).textTheme.titleSmall),
                              ),
                            ),
                          

                          ],
                        ),
                      ),
                    ),
                  ),
                  if(vRoom.isActive == 'TRUE') ConsumerCard(room: vRoom,)
                  else
                  Center(child: Text('Phòng hiện đang trống',
                  style: TextStyle(
            color: Colors.grey.withValues(alpha: .8),
            fontStyle: FontStyle.italic,
          ) ,),)
              ],
            ),
          ): SizedBox();
        }
      ),
    );
  }
}