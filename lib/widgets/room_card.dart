import 'package:flutter/material.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/widgets/expanded_floor_card.dart';

class RoomCard extends StatelessWidget {
  const RoomCard({super.key, required this.room, required this.isOpen});
  final Room room;
  final Function() isOpen;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isOpen,

      child: Card(
        color:
            room.isActive == 'TRUE'
                ? Colors.greenAccent.withValues(alpha: .6)
                : Colors.white,
        child: SizedBox.square(
          dimension: 96,
          child: Stack(
            children: [
              Center(child: Text(room.roomId ?? '')),

              if (room.type?.replaceAll(' ', '') ==
                  RoomType.vipstandard.name.toUpperCase())
                Positioned(
                  top: 0,
                  right: 0,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      color: Colors.yellow.shade200,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text('VIP'),
                    ),
                  ),
                ),
              if (room.type?.replaceAll(' ', '') ==
                  RoomType.deluxe.name.toUpperCase())
                Positioned(
                  top: 0,
                  right: 0,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      color: Colors.cyan.shade200,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text('DELUXE'),
                    ),
                  ),
                ),
              if (room.type?.replaceAll(' ', '') ==
                  RoomType.suite.name.toUpperCase())
                Positioned(
                  top: 0,
                  right: 0,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      color: Colors.lightBlueAccent.shade200,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text('SUITE'),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
 