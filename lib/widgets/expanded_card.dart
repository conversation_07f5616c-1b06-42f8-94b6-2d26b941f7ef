// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:songanh/main.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/widgets/expanded_floor_card.dart';

import 'package:songanh/widgets/value_notifier_list.dart';

class ExpandedCard extends StatefulWidget {
  const ExpandedCard({super.key, required this.list});

  final List<Room> list;

  @override
  State<ExpandedCard> createState() => _ExpandedCardState();
}

class _ExpandedCardState extends State<ExpandedCard> {
  final ValueNotifierList<Room> floor1 = ValueNotifierList([]);
  final ValueNotifierList<Room> floor2 = ValueNotifierList([]);
  final ValueNotifierList<Room> floor3 = ValueNotifierList([]);
  final ValueNotifierList<Room> floor4 = ValueNotifierList([]);

  @override
  void initState() {
    floor1.setValue(
      widget.list
          .where((final e1) => e1.floor?.contains('1') ?? false)
          .toList(),
    );
    floor2.setValue(
      widget.list
          .where((final e1) => e1.floor?.contains('2') ?? false)
          .toList(),
    );
    floor3.setValue(
      widget.list
          .where((final e1) => e1.floor?.contains('3') ?? false)
          .toList(),
    );
    floor4.setValue(
      widget.list
          .where((final e1) => e1.floor?.contains('4') ?? false)
          .toList(),
    );
    super.initState();
  }
@override
  void dispose() {
   MyApp.firstTimeOneTab = true;
    super.dispose();
  }
  @override
  Widget build(final BuildContext context) {
    return SingleChildScrollView(
      child: AnimationLimiter(
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
              duration: Duration(milliseconds: MyApp.firstTimeOneTab ? 0: 375),
              childAnimationBuilder: (widget) => SlideAnimation(
                horizontalOffset: 50.0,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ValueListenableBuilder(
                valueListenable: floor1,
                builder: (context, vRoom, child) {
                  return ExpandedFloorCard(
                    rooms: vRoom,
                  );
                },
              ),
            ),
            // TANG 2
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ValueListenableBuilder(
                  valueListenable: floor2,
                  builder: (context, vRoom, child) {
                    return ExpandedFloorCard(
                      rooms: vRoom,
                    );
                  }),
            ),
            // TANG 3
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ValueListenableBuilder(
                valueListenable: floor3,
                builder: (context, vRoom, child) {
                  return ExpandedFloorCard(
                    rooms: vRoom,
                  );
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ValueListenableBuilder(
                valueListenable: floor4,
                builder: (context, vRoom, child) {
                  return ExpandedFloorCard(
                    rooms: vRoom,
                  );
                },
              ),
            ),
          ],
            )
         
        ),
      ),
    );
  }
}
