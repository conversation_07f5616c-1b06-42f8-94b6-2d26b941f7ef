import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/routes.dart';
import 'dart:ui' as ui;

enum RoomType {
  deluxe,
  suite,
  vipstandard

}

class ExpandedFloorCard extends StatefulWidget {
  const ExpandedFloorCard({super.key, required this.rooms});
  final List<Room> rooms;

  @override
  State<ExpandedFloorCard> createState() => _ExpandedFloorCardState();
}

class _ExpandedFloorCardState extends State<ExpandedFloorCard> {
  late final ValueNotifier<Item> valItem;
  @override
  void initState() {
    valItem = ValueNotifier(
      Item(room: widget.rooms),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: valItem,
        builder: (context, vRoom, child) {
          final roomActive = vRoom.room
              .where((final r) => r.isActive == 'TRUE');
          final total = roomActive.fold(
              0, (l, r) => l + (int.tryParse(r.gia ?? '') ?? 0));
          return _buildFloor(valItem, roomActive, total);
        });
  }

  ValueListenableBuilder<Item> _buildFloor(
      ValueNotifier<Item> valItem, Iterable<Room> roomActive, int total) {
    return ValueListenableBuilder(
      valueListenable: valItem,
      builder: (context, vItem, child) {
        return SizedBox(
          width: MediaQuery.sizeOf(context).width - 24,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Tầng ${vItem.room.first.floor}',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    if (roomActive.isNotEmpty)
                      Text.rich(TextSpan(text: '', children: [
                        TextSpan(
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w800,
                                  color: Colors.green.withValues(alpha: .9) ),
                            text:
                                '+ ${NumberFormat('#,###', 'vi_VN').format(total)} VND')
                      ])),
                  ],
                ),
              ),
              ExpansionTile(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                    width: 1,
                  ),
                ),
                showTrailingIcon: roomActive.isNotEmpty,
                title: _buildTitle(
                    'Tầng ${vItem.room.first.floor}', roomActive.length, total),
                subtitle: Text.rich(
                  TextSpan(
                  style: TextStyle(color: Colors.lightBlue),
                  text: 'Xem chi tiết',
                  recognizer: TapGestureRecognizer()
                        ..onTap = ()async{
                            AppNavigator.pushDetailFloor(context, widget.rooms);
                        }
                  ),
                ),
                onExpansionChanged: (value) {
                  valItem.value = vItem.copyWith(
                    isExpanded: !vItem.isExpanded,
                  );
                },
                children: [
                  for (final active in roomActive)
                    ListTile(
                      title: Text.rich(TextSpan(children: [
                        TextSpan(text: active.roomId),
                        TextSpan(text: ' '),
                        if (active.type?.replaceAll(' ', '') == RoomType.vipstandard.name.toUpperCase())
                          WidgetSpan(
                            alignment: ui.PlaceholderAlignment.middle,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                  color: Colors.yellow.shade200,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                child: Text('VIP STANDARD'),
                              ),
                            ),
                          )
                         else if(active.type?.replaceAll(' ', '') == RoomType.deluxe.name.toUpperCase()) 
                         WidgetSpan(
                            alignment: ui.PlaceholderAlignment.middle,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                  color: Colors.cyan.shade200,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                child: Text('DELUXE'),
                              ),
                            ),
                          )  else if(active.type?.replaceAll(' ', '') == RoomType.suite.name.toUpperCase()) 
                         WidgetSpan(
                            alignment: ui.PlaceholderAlignment.middle,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                  color: Colors.lightBlueAccent.shade200,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                child: Text('SUITE'),
                              ),
                            ),
                          ),
                        if (active.gia != null)
                          TextSpan(text: '', children: [
                            TextSpan(
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w800,
                                  color: Colors.green.withValues(alpha: .9) ),
                                text:
                                    ' +${NumberFormat('#,###', 'vi_VN').format(int.tryParse(active.gia ?? '') ?? 0)} VND')
                          ])
                      ])),
                      subtitle: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                         if(active.timeIn?.isNotEmpty??false) 
                         _buildTine("Nhận phòng: ", active.timeIn ?? ''),
                        if(active.timeOut?.isNotEmpty??false) 
                        _buildTine("Trả phòng: ", active.timeOut ?? ''),
                        ],
                      ),
                    )
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Text _buildTine(String title, String time) {
    DateTime dateTime = DateFormat('HH:mm dd/MM/yyyy').parse(time).toLocal();
    return Text.rich(TextSpan(
        text: title,
        children: [
          TextSpan(
            text: DateFormat('HH:mm - dd/MM/yyyy').format(dateTime),
            style: TextStyle(
                fontSize: 18,
                backgroundColor: Colors.grey.withValues(alpha: .1),
                fontWeight: FontWeight.bold),
          )
        ]));
  }

  Row _buildTitle(final String title, final int nActive, final int doanhthu) {
    return Row(
      children: [
        // Text(
        //   title,
        //   style: Theme.of(context).textTheme.titleLarge,
        // ),
        // SizedBox(
        //   width: 8,
        // ),
        if (nActive != 0)
          Text.rich(
            TextSpan(
              children: [
                WidgetSpan(
                  alignment: ui.PlaceholderAlignment.middle,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                    child: SizedBox.square(dimension: 12),
                  ),
                ),
                TextSpan(text: ' ${nActive.toString()} phòng đang hoạt động'),
              ],
            ),
          )else
          Text('Hiện tại còn phòng trống', style: TextStyle(
            color: Colors.grey.withValues(alpha: .8),
            fontStyle: FontStyle.italic,
          ),)
          ,

      ],
    );
  }
}

class Item {
  final List<Room> room;
  bool isExpanded;

  Item({required this.room, this.isExpanded = false});

  Item copyWith({List<Room>? room, bool? isExpanded}) {
    return Item(
      room: room ?? this.room,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}
