import 'dart:ui' as ui;

import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/widgets/consumer_card.dart';
import 'package:songanh/widgets/expanded_floor_card.dart';
import 'package:songanh/widgets/room_card.dart';

class AllRoomBody extends StatelessWidget {
  const AllRoomBody({super.key, required this.list});
  final List<Room> list;
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        itemCount: 4,
        itemBuilder: (_, i) {
          final rooms = list.where(
            (element) => element.floor == (i + 1).toString(),
          );
          final total =
              rooms.fold(0, (l, r) => l + (int.tryParse(r.gia ?? '') ?? 0));
          return Column(
            children: [
              ColoredBox(
                color: Colors.grey.withValues(alpha: .1),
                child: SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Tầng ${rooms.first.floor}'),
                        Text(
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w800,
                                  color: Colors.green.withValues(alpha: .9) ),
                            '+ ${NumberFormat('#,###', 'vi_VN').format(total)} VND')
                      ],
                    ),
                  ),
                ),
              ),
              Wrap(
                children: [
                  ...List.generate(rooms.length, (i2) {
                    return RoomCard(
                      room: rooms.toList()[i2],
                      isOpen:rooms.toList()[i2].isActive == 'TRUE'?  (){
                            showModal(
          context: context,
          builder: (builder) {
            final room = rooms.toList()[i2];
            return AlertDialog(
              insetPadding: EdgeInsets.zero,
              actionsPadding: EdgeInsets.zero,
              title: Text.rich(TextSpan(children: [
                TextSpan(text: 'Phòng ${rooms.toList()[i2].roomId}'),
                TextSpan(text: ' '),
                     if (room.type?.replaceAll(' ', '') == RoomType.vipstandard.name.toUpperCase())
                          WidgetSpan(
                            alignment: ui.PlaceholderAlignment.middle,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                  color: Colors.yellow.shade200,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                child: Text('VIP STANDARD'),
                              ),
                            ),
                          )
                         else if(room.type?.replaceAll(' ', '') == RoomType.deluxe.name.toUpperCase()) 
                         WidgetSpan(
                            alignment: ui.PlaceholderAlignment.middle,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                  color: Colors.cyan.shade200,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                child: Text('DELUXE'),
                              ),
                            ),
                          )  else if(room.type?.replaceAll(' ', '') == RoomType.suite.name.toUpperCase()) 
                         WidgetSpan(
                            alignment: ui.PlaceholderAlignment.middle,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                  color: Colors.lightBlueAccent.shade200,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                child: Text('SUITE'),
                              ),
                            ),
                          ),
              ])),
              content: SizedBox(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height / 3,
                child: ConsumerCard(room: rooms.toList()[i2]),
              ),
            );
          },
        );
                      }: (){
                        
                      },
                    );
                  })
                ],
              )
            ],
          );
        });
  }
}
