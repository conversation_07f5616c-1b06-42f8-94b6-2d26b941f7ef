import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/query/query_history_page.dart';

class ConsumerCard extends StatelessWidget {
  const ConsumerCard({super.key, required this.room});
  final Room room;

  @override
  Widget build(BuildContext context) {
    final dateCheckin = QueryHistoryPage.calculateHotelDaysAndNights(room.timeIn??'', room.timeOut??'');
    return Card(
      //  elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _infoRow(context, 'Tên khách:', room.consumerName ?? ''),
            const SizedBox(height: 8),
            _infoRow(context, 'Số điện thoại:', room.phone ?? ''),
            const SizedBox(height: 8),
            _infoRow(context, 'Thời gian ở:', 
              '${dateCheckin.day} ngày ${dateCheckin.night} đêm'),
            const SizedBox(height: 8),
            _infoRow(context, 'Tổng tiền:',
                '${NumberFormat('#,###', 'vi_VN').format(int.tryParse(room.gia ?? '') ?? 0)} VND',
                color: Colors.green),
            const SizedBox(height: 8),
            _buildTine('Nhận phòng: ', room.timeIn ?? ''),
            const SizedBox(height: 8),
            _buildTine('Trả phòng: ', room.timeOut ?? ''),
          ],
        ),
      ),
    );
  }

  Text _buildTine(
    String title,
    String time,
  ) {
    DateTime dateTime = DateFormat('HH:mm dd/MM/yyyy').parse(time).toLocal();
    return Text.rich(TextSpan(
        text: title,
        style: TextStyle(fontWeight: FontWeight.normal),
        children: [
          TextSpan(
            text: DateFormat('HH:mm - dd/MM/yyyy').format(dateTime),
            style: TextStyle(
                fontSize: 18,
                backgroundColor: Colors.grey.withValues(alpha: .1),
                fontWeight: FontWeight.bold),
          )
        ]));
  }

  Widget _infoRow(BuildContext context, String label, String value,
      {Color? color = Colors.black}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontWeight: FontWeight.normal),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style:
                Theme.of(context).textTheme.titleLarge?.copyWith(color: color),
          ),
        ),
      ],
    );
  }
}
