import 'package:json_annotation/json_annotation.dart';

part 'room.g.dart';

@JsonSerializable(createToJson: false)
class Room {
  @J<PERSON><PERSON><PERSON>(name: 'MA_PHONG')
  final String? roomId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'TANG')
  final String? floor;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'KIEU_PHONG')
  final String? type;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'GIA')
  final String? gia;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'TEN_KH')
  final String? consumerName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'SDT')
  final String? phone;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'TINH_TRANG_PHONG')
  final String? isActive;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'THOI_GIAN_NHAN')
  final String? timeIn;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'THOI_GIAN_O')
  final String? time;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'THOI_GIAN_TRA')
  final String? timeOut;

  Room(
      {this.roomId,
      this.floor,
      this.type,
      this.gia,
      this.consumerName,
      this.phone,
      this.isActive,
      this.timeIn,
      this.time,
      this.timeOut});
  factory Room.fromJson(Map<String, dynamic> json) => _$<PERSON>(json);
}
