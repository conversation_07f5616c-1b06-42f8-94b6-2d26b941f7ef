import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  static ThemeData theme = ThemeData(
      fontFamily: GoogleFonts.roboto(
        fontSize: 36,
      ).fontFamily,
      textTheme: TextTheme(
        bodySmall: GoogleFonts.roboto(fontSize: 12),
        bodyMedium: GoogleFonts.roboto(fontSize: 16),
        bodyLarge:
            GoogleFonts.roboto(fontSize: 18, fontWeight: FontWeight.w500),
        titleSmall:
            GoogleFonts.roboto(fontSize: 14, fontWeight: FontWeight.bold),
        titleMedium:
            GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.bold),
        titleLarge:
            GoogleFonts.roboto(fontSize: 20, fontWeight: FontWeight.bold),
        labelSmall: GoogleFonts.roboto(fontSize: 10),
        labelMedium:
            GoogleFonts.roboto(fontSize: 12, fontWeight: FontWeight.w500),
        labelLarge:
            GoogleFonts.roboto(fontSize: 14, fontWeight: FontWeight.w500),
        displaySmall:
            GoogleFonts.roboto(fontSize: 24, fontWeight: FontWeight.bold),
        displayMedium:
            GoogleFonts.roboto(fontSize: 32, fontWeight: FontWeight.bold),
        displayLarge:
            GoogleFonts.roboto(fontSize: 48, fontWeight: FontWeight.bold),
      ),
      primaryColor: Colors.deepPurple,
      colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(),
      ));
}
