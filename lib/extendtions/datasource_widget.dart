import 'package:flutter/material.dart';
import 'package:songanh/models/room.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class RoomDataSource extends DataGridSource {
  /// Creates the employee data source class with required details.
  RoomDataSource({required List<Room> employeeData}) {
    _employeeData = employeeData
        .map<DataGridRow>((e) => DataGridRow(cells: [
              DataGridCell<String>(columnName: 'id', value: e.roomId),
              DataGridCell<String>(columnName: 'ten kh', value: e.consumerName),
              DataGridCell<String>(columnName: 'sdt', value: e.phone),
              DataGridCell<String>(columnName: 'gia', value: e.gia),
              DataGridCell<String>(columnName: 'gia2', value: e.gia),
              DataGridCell<String>(columnName: 'gia3', value: e.gia),
            ]))
        .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
        cells: row.getCells().map<Widget>((e) {
      return Container(
        alignment: Alignment.center,
        child: Text(e.value.toString()),
      );
    }).toList());
  }
}
