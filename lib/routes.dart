import 'package:flutter/material.dart';
import 'package:gsheets/gsheets.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/page/all_room_page.dart';
import 'package:songanh/page/detail_floor_page.dart';

class AppNavigator {
  static void pushAllRoom(
      final BuildContext context, final Worksheet workSheet) {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (builder) => AllRoomPage(
                  worksheet: workSheet,
                )));
  }

  static void pushDetailFloor(
      final BuildContext context, final List<Room> rooms) {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (builder) => DetailFloorPage(
                  rooms: rooms,
                )));
  }
}
