import 'package:flutter/material.dart';
import 'package:gsheets/gsheets.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/services/rooms/room_service.dart';
import 'package:songanh/widgets/expanded_card.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key, required this.wSheet});
  final Worksheet wSheet;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Room>>(
        future: RoomService.getRoomInfo(widget.wSheet),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
              child: CircularProgressIndicator(),
            );
          }
          if (snapshot.connectionState == ConnectionState.done) {
            return ExpandedCard(
              list: snapshot.data ?? [],
            );
          }
          return SizedBox();
        });
  }
}
