// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import 'package:songanh/models/room.dart';

class ChartPage extends StatefulWidget {
  const ChartPage({super.key, required this.rooms});
  final List<Room> rooms;
  @override
  State<ChartPage> createState() => _ChartPageState();
}

class _ChartPageState extends State<ChartPage> {
  late List<SalesData> _chartData;
  late TooltipBehavior _tooltipBehavior;
  late ZoomPanBehavior _zoomPanBehavior;
  late TrackballBehavior? _trackballBehavior;
  List<SalesData> roomFold = [];
  @override
  void initState() {
     _trackballBehavior = TrackballBehavior(
      enable: true,
      activationMode: ActivationMode.singleTap,
      tooltipSettings: const InteractiveTooltip(format: 'point.x : point.y'),
    );
    _chartData = getChartData();
    _tooltipBehavior = TooltipBehavior(
      enable: true,
      canShowMarker: true,
      format: 'point.x : point.y ',
      header: ''
      );
    _zoomPanBehavior = ZoomPanBehavior(
        enablePinching: true,
        enableDoubleTapZooming: true,
        enableSelectionZooming: true,
        selectionRectBorderColor: Colors.red,
        selectionRectBorderWidth: 2,
        selectionRectColor: Colors.grey,
        enablePanning: true,
        zoomMode: ZoomMode.x,
        enableMouseWheelZooming: true,
        maximumZoomLevel: .1);
 
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            appBar: AppBar(),
            body:_buildCartesianChart()
            //  SfCartesianChart(
            //     title: ChartTitle(text: 'Yearly sales analysis (USD)'),
            //    // legend: Legend(isVisible: false),
            //     tooltipBehavior: _tooltipBehavior,
            //     zoomPanBehavior: _zoomPanBehavior,
            //     trackballBehavior: _trackballBehavior,
            //     series: <LineSeries<SalesData, DateTime>>[
            //       LineSeries<SalesData, DateTime>(
            //           name: 'Doanh thu',
            //           dataSource: _chartData,
            //           xValueMapper: (SalesData sales, _) => sales.year,
            //           yValueMapper: (SalesData sales, _) => sales.sales,
            //           dataLabelSettings: DataLabelSettings(isVisible: false, showZeroValue: false),
            //           enableTooltip: true)
            //     ],
            //     primaryXAxis: DateTimeAxis(
            //         edgeLabelPlacement: EdgeLabelPlacement.shift,
            //         dateFormat: DateFormat('dd/MM'),
            //         maximumLabels: _chartData.length,
            //         intervalType: DateTimeIntervalType.auto,
            //         interactiveTooltip: InteractiveTooltip(enable: false)),
            //     primaryYAxis: NumericAxis(
            //         labelFormat: '{value} vnđ',
            //         numberFormat: NumberFormat.simpleCurrency(decimalDigits: 0),
            //         interactiveTooltip: InteractiveTooltip(enable: false)))
                 
                    ));
  }
  SfCartesianChart _buildCartesianChart() {
  
    return SfCartesianChart(
      zoomPanBehavior: _zoomPanBehavior,
      plotAreaBorderWidth: 0,
      title: ChartTitle(
        text: 'Thống kê doanh thu',
      ),
      primaryXAxis: CategoryAxis(
        labelStyle: TextStyle(
           color: Colors.black
        ),
        axisLine: const AxisLine(width: 0),
        labelPosition: ChartDataLabelPosition.inside,
        majorTickLines: const MajorTickLines(width: 0),
        majorGridLines: const MajorGridLines(width: 0),
      ),
      primaryYAxis: const NumericAxis(
        isVisible: true,
        minimum: 0,
        maximum: 2000000,
      ),
      series: _buildColumnSeries(),
      tooltipBehavior: _tooltipBehavior,
    );
  }

  /// Returns the list of Cartesian Column series.
  List<ColumnSeries<SalesData, String>> _buildColumnSeries() {
    return <ColumnSeries<SalesData, String>>[
      ColumnSeries<SalesData, String>(
        dataSource: _chartData,
        xValueMapper: (SalesData sales, int index) => DateFormat('dd/MM').format(sales.year),
        yValueMapper: (SalesData sales, int index) => sales.sales,

        /// If we set the border radius value for Column series,
        /// then the series will appear as rounder corner.
        borderRadius: BorderRadius.circular(10),
        spacing: .1,
        width: .9,
        dataLabelSettings: const DataLabelSettings(
          isVisible: true,
          showZeroValue: false,
        //  showCumulativeValues: true,
          labelAlignment: ChartDataLabelAlignment.outer,
          labelPosition: ChartDataLabelPosition.outside
        ),
      ),
    ];
  }

  List<SalesData> getChartData() {
    for (var el in widget.rooms) {
        final rDate = DateFormat('hh:mm dd/MM/yyyy').parse(el.timeIn??'');
        final total = el.gia;
        // print(rDate);
        // print(total);
       final checkIndex = roomFold.indexWhere((final e){
       //   final date = DateFormat('hh:mm dd/MM/yyyy').parse(e.year);
          return (rDate.day == e.year.day) && 
          (rDate.month == e.year.month) && 
          (rDate.year == e.year.year);
        });
        if(checkIndex == -1){
            roomFold.add(SalesData(rDate, double.tryParse(total??'')??0));
        }else{
          roomFold[checkIndex] = roomFold[checkIndex].copyWith(sales: 
          roomFold[checkIndex].sales + (double.tryParse(total?? '')?? 0));
            
        }

     }
    return roomFold;
    // widget.rooms.map((final e)=> SalesData(DateFormat('hh:mm dd/MM/yyyy').parse(e.timeIn??''),
    //  double.tryParse(e.gia ?? '')?? 0)).toList();
  }
}

class SalesData {
  SalesData(this.year, this.sales);
  final DateTime year;
  final double sales;

  SalesData copyWith({
    DateTime? year,
    double? sales,
  }) {
    return SalesData(
      year ?? this.year,
      sales ?? this.sales,
    );
  }
}
