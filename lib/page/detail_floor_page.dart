import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:intl/intl.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/widgets/expanded_animated_card.dart';
import 'package:songanh/widgets/room_card.dart';

class DetailFloorPage extends StatefulWidget {
  const DetailFloorPage({super.key, required this.rooms});
  final List<Room> rooms;

  @override
  State<DetailFloorPage> createState() => _DetailFloorPageState();
}

class _DetailFloorPageState extends State<DetailFloorPage> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final ValueNotifier<Room?> nRoom = ValueNotifier(null); 
  @override
  void initState() {
     _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    super.initState();
  }
    Future<void> _updateCalendarUI() async {
            _animationController.reset();
           await _animationController.forward();
     
    
   
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 8.0,
                vertical: 12,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Tầng ${widget.rooms.first.floor}'),
                  Text(
                     style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w800,
                                  color: Colors.green.withValues(alpha: .9) ),
                      '+${NumberFormat('#,###', 'vi_VN').format(widget.rooms.where((final room) => room.isActive == 'TRUE').fold(0, (final l, final r) => l + (int.tryParse(r.gia ?? '') ?? 0)))} VND')
                ],
              ),
            ),
          ),
          AnimationLimiter(
            child: Wrap(
              children: AnimationConfiguration.toStaggeredList(
              duration: Duration(milliseconds: 375),
              childAnimationBuilder: (widget) => SlideAnimation(
                horizontalOffset: 50.0,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children:   [
                ...List.generate(widget.rooms.length, (i) {
                  return RoomCard(
                    room: widget.rooms[i],
                    isOpen: (){
                      nRoom.value =widget.rooms[i];
                      _updateCalendarUI();
            
                    },
                  );
                })
              ],
              )
           
            ),
          ),
          ExpandedAnimatedCard(
            nRoom: nRoom,
            controller:_animationController )
        ],
      ),
    );
  }
}
