import 'package:flutter/material.dart';
import 'package:gsheets/gsheets.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/services/rooms/room_service.dart';
import 'package:songanh/widgets/all_room_body.dart';

class AllRoomPage extends StatelessWidget {
  const AllRoomPage({super.key, required this.worksheet});
  final Worksheet worksheet;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(),
        body: FutureBuilder<List<Room>>(
            future: RoomService.getRoomInfo(worksheet),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(
                  child: CircularProgressIndicator(),
                );
              }
              if (snapshot.connectionState == ConnectionState.done) {
                return AllRoomBody(
                  list: snapshot.data ?? [],
                );
              }
              return SizedBox();
            }));
  }
}
