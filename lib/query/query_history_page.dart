import 'package:intl/intl.dart';
import 'package:songanh/models/room.dart';

class QueryHistoryPage {
  // <PERSON><PERSON><PERSON> lọc theo khoảng thời gian
  static List<Room> filterOrdersByMonths(List<Room> orders, int months) {
    final now = DateTime.now().toLocal();
    DateTime start = DateTime(now.year, now.month - months, now.day);
    DateTime end = DateTime(now.year, now.month, now.day);

    return orders.where((order) {
      DateTime orderDate =
          DateFormat('HH:mm dd/MM/yyyy').parse(order.timeIn ?? '');
      DateTime orderDay =
          DateTime(orderDate.year, orderDate.month, orderDate.day);
      return !orderDay.isBefore(start) && !orderDay.isAfter(end);
    }).toList()
      ..sort((a, b) {
        // Sắp xếp theo ngày gần nhất với referenceDate
        DateTime aDate = DateFormat('HH:mm dd/MM/yyyy').parse(a.timeIn ?? '');
        DateTime bDate = DateFormat('HH:mm dd/MM/yyyy').parse(b.timeIn ?? '');
        return aDate.compareTo(bDate);
      });
  }

  static List<Room> filterOrdersByDateRange(
      List<Room> orders, DateTime start, DateTime end) {
    return orders.where((room) {
      DateTime orderDate =
          DateFormat('HH:mm dd/MM/yyyy').parse(room.timeIn ?? '');
      // Đưa về 00:00:00 để so sánh theo ngày
      DateTime orderDay =
          DateTime(orderDate.year, orderDate.month, orderDate.day);
      DateTime startDay = DateTime(start.year, start.month, start.day);
      DateTime endDay = DateTime(end.year, end.month, end.day);
      return !orderDay.isBefore(startDay) && !orderDay.isAfter(endDay);
    }).toList()
      ..sort((a, b) {
        DateTime aDate = DateFormat('HH:mm dd/MM/yyyy').parse(a.timeIn ?? '');
        DateTime bDate = DateFormat('HH:mm dd/MM/yyyy').parse(b.timeIn ?? '');
        return aDate.compareTo(bDate);
      });
  }

  static CheckinDate calculateHotelDaysAndNights(String checkInStr, String checkOutStr) {
  final dateFormat = DateFormat("HH:mm dd/MM/yyyy");
  final checkIn = dateFormat.parse(checkInStr);
  final checkOut = dateFormat.parse(checkOutStr);

  // Tính số ngày (bao gồm cả ngày check-in và check-out)
  int totalDays = checkOut.difference(checkIn).inDays + 1;

  // Tính số đêm
  DateTime firstNight;
  if (checkIn.hour < 12) {
    firstNight = DateTime(checkIn.year, checkIn.month, checkIn.day);
  } else {
    firstNight = DateTime(checkIn.year, checkIn.month, checkIn.day).add(Duration(days: 1));
  }

  DateTime lastNight;
  if (checkOut.hour < 12) {
    lastNight = DateTime(checkOut.year, checkOut.month, checkOut.day).subtract(Duration(days: 1));
  } else {
    lastNight = DateTime(checkOut.year, checkOut.month, checkOut.day);
  }

  int totalNights = lastNight.difference(firstNight).inDays + 1;
  totalNights = totalNights < 0 ? 0 : totalNights;

  return CheckinDate(day: totalDays, night: totalNights);
}
}
class CheckinDate{
  final int day;
  final int night;

  CheckinDate({required this.day, required this.night});
}